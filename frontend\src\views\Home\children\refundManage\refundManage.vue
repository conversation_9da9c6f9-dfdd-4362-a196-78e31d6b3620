<template>
  <div class="refund-manage">
    <!-- 用户信息栏 -->
    <div class="user-info-bar">
      <div class="user-info">
        <span class="user-label">用户信息：</span>
        <span class="user-id">{{ userStore.user_info.username }}</span>
        <span class="points-label">积分余额：</span>
        <span class="points-value">{{ userStore.user_info.balance }}</span>
        <!-- <el-button type="info" size="small" circle class="help-btn">
          <el-icon><QuestionFilled /></el-icon>
        </el-button> -->
        <el-button type="info" size="small" circle class="refresh-btn" @click="getDateUser">
          <el-icon>
            <Refresh />
          </el-icon>
        </el-button>
        <!-- <div class="store-select">
          <span class="store-label">操作店铺：</span>
          <el-select v-model="searchState.mallId" placeholder="请选择店铺" style="width: 200px;">
            <el-option
              v-for="store in mallStore.tableList"
              :key="store.mallId"
              :label="store.mallName"
              :value="store.mallId">
            </el-option>
          </el-select>
        </div> -->
      </div>
      <div class="action-buttons">
        <el-button type="primary" size="default" @click="handleGetGoods">获取商品</el-button>
        <el-button type="success" size="default" @click="handleApplyOrder" :loading="applyOrderLoading">申请订单</el-button>
        <el-button type="danger" size="default" @click="handleDeleteOrder">删除订单</el-button>
      </div>
    </div>

    <!-- 状态标签 -->
    <div class="status-tabs">
      <el-space>
        <div class="search-area">
          <div class="status-all">
            <el-button :type="searchForm.order_status === 101 ? 'primary' : 'default'" @click="handleStatusFilter(101)">
              全部
            </el-button>
            <el-button :type="searchForm.order_status === 0 ? 'primary' : 'default'" @click="handleStatusFilter(0)">
              待申请
            </el-button>
            <el-button :type="searchForm.order_status === 1 ? 'primary' : 'default'" @click="handleStatusFilter(1)">
              申请中
            </el-button>
            <el-button :type="searchForm.order_status === 2 ? 'primary' : 'default'" @click="handleStatusFilter(2)">
              执行中
            </el-button>
            <el-button :type="searchForm.order_status === 3 ? 'primary' : 'default'" @click="handleStatusFilter(3)">
              已完成
            </el-button>
            <el-button :type="searchForm.order_status === 4 ? 'primary' : 'default'" @click="handleStatusFilter(4)">
              执行失败
            </el-button>
            <el-button @click="handleSyncOrderStatus" type="primary" size="default"
              style="margin-left: 8px;">同步订单状态</el-button>
          </div>
          <div class="search-body">
            <el-input v-model="searchForm.goods_id" placeholder="请输入宝贝ID" style="width: 200px;" clearable
              @keyup.enter="handleGoodsIdSearch" @clear="handleClearSearch">
            </el-input>
            <el-button type="primary" @click="handleGoodsIdSearch" style="margin-left: 8px;">
              搜索
            </el-button>
          </div>
        </div>
      </el-space>
    </div>

    <!-- 数据表格 -->
    <el-table stripe @selection-change="(data: typeof state.selection) => { state.selection = data }"
      :data="state.refundList" class="refund-table" ref="refundTableEl" height="420">
      <template #empty>
        <el-empty description="暂无订单记录">
          <template #description>
            <p>当前状态下没有订单数据</p>
            <p>请尝试切换其他状态或添加新的商品订单</p>
          </template>
        </el-empty>
      </template>
      <el-table-column type="selection" width="30"></el-table-column>
      <el-table-column label="序号" type="index" width="60"
        :index="(index: number) => (pagination.page - 1) * pagination.limit + index + 1"></el-table-column>
      <el-table-column label="店铺名称" prop="shopName" width="150"></el-table-column>
      <el-table-column label="宝贝ID" prop="goodsId" width="120">
        <template #default="scope">
          <el-link :underline="false" @click="copyStr(scope.row.goodsId)">{{ scope.row.goodsId }}</el-link>
        </template>
      </el-table-column>
      <el-table-column label="宝贝名称" prop="goodsName" min-width="200" show-overflow-tooltip></el-table-column>
      <el-table-column label="状态" width="100" prop="status">
        <template #default="scope">
          <el-tag v-if="scope.row.status == 0" type="info">{{ getStatusText(scope.row.status) }}</el-tag>
          <el-tag v-if="scope.row.status == 1" type="warning">{{ getStatusText(scope.row.status) }}</el-tag>
          <el-tag v-if="scope.row.status == 2" type="">{{ getStatusText(scope.row.status) }}</el-tag>
          <el-tag v-if="scope.row.status == 3" type="success">{{ getStatusText(scope.row.status) }}</el-tag>
          <el-tag v-if="scope.row.status == 4" type="danger">{{ getStatusText(scope.row.status) }}</el-tag>
          <!--<el-tag >{{ getStatusText(scope.row.status) }}</el-tag>
           <el-tag :type="getStatusType(scope.row.status)">{{ getStatusText(scope.row.status) }}</el-tag> -->
        </template>
      </el-table-column>
      <el-table-column label="商品价值" prop="goodsValue" width="120">
        <template #default="scope">
          <span class="amount">¥{{ scope.row.goodsValue.toFixed(2) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="创建时间" prop="createTime" width="160">
        <template #default="scope">
          <span v-if="scope.row.createTime">
            {{ dayjs(scope.row.createTime).format('YYYY-MM-DD HH:mm:ss') }}
          </span>
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column label="扣除积分" prop="blacklistScore" width="100">
        <template #default="scope">
          <span class="score">{{ scope.row.blacklistScore.toFixed(2) }}</span>
        </template>
      </el-table-column>
      <!-- <el-table-column label="操作" width="200" align="center" prop="" fixed="right">
        <template #default="scope">
          <el-space>
            <el-button 
              v-if="scope.row.status === 'pending'" 
              type="success" 
              size="small"
              @click="handleApprove(scope.row)">
              同意退款
            </el-button>
            <el-button 
              v-if="scope.row.status === 'pending'" 
              type="danger" 
              size="small"
              @click="handleReject(scope.row)">
              拒绝退款
            </el-button>
            <el-button 
              type="primary" 
              size="small"
              @click="handleViewDetail(scope.row)">
              查看详情
            </el-button>
          </el-space>
        </template>
      </el-table-column> -->
    </el-table>

    <!-- 分页组件 -->
    <ds-pagination :total="pagination.total" v-model:current-page="pagination.page" v-model:page-size="pagination.limit"
      @current-change="getRefundList()" @size-change="getRefundList()" :selection="state.selection" />

    <!-- 说明标签 -->
    <div class="illustrate-tabs">
      <el-space>
        <div class="container" style="padding: 0;font-size: 14px;height: 150px;">
          <h5></h5>
          <h3 class="title f-s-16" style="padding: 0px 0 0 25px;">
            拍退声明
          </h3>

          <ol>
            <span style="margin-top: 0.5em;">1，链接上架不能超过15天，只能0销量才能提交;</span>
            <span style="margin-top: 0.5em;margin-left: 7em;">4，拍退激活失败只能再提交一次申请，第二次还是失败的链接不能再提交;</span><br>
            <span style="margin-top: 1em;">2，商品价格300元以内，高于300的商品上个低价的sku;</span>
            <span style="margin-top: 1em;margin-left: 3.6em;">5，符合上述要求的，不出销量可以返还积分，不符合的则不返还积分。</span><br>
            <span style="margin-top: 1em;">3，代拍前一两天或当天改销量;</span>
            <span style="margin-top: 1em;margin-left: 14.7em;color: #f9638c;">6，需要开通极速退款，或及时自行退款，需当天完成退款。</span><br>
          </ol>
          <span style="padding-left: 60px;color: #f9638c;"> 拍退激活后，第二天下午6点前显示
            食品，生鲜，茶叶，水果和图书，酒水，黑五，
            这种特殊类目，成功率只有50% ,失败不返还积分</span>
        </div>
      </el-space>
    </div>

    <!-- 获取商品弹窗 -->
    <el-dialog v-model="goodsDialog.visible" title="实时获取商品" width="90%" :close-on-click-modal="false"
      class="goods-dialog">

      <!-- 弹窗头部操作区 -->
      <div class="dialog-header">
        <!-- 第一行：搜索和店铺选择 -->
        <div class="search-row">
          <div class="search-controls">
            <el-input v-model="goodsDialog.searchParams.goods_id" placeholder="宝贝ID搜索,逗号隔开" style="width: 200px;"
              clearable @keyup.enter="loadGoodsData" @clear="loadGoodsData">
            </el-input>
          </div>
          <div class="right-controls">
            <div class="store-select-wrapper">
              <span class="store-label">店铺：</span>
              <el-select v-model="goodsDialog.searchParams.mallId" placeholder="请选择店铺" style="width: 200px;"
                @change="handleStoreChange">
                <el-option v-for="store in mallStore.tableList" :key="store.mallId" :label="store.mallName"
                  :value="store.mallId">
                </el-option>
              </el-select>
            </div>
            <el-button type="primary" @click="loadGoodsData" :loading="goodsDialog.loading" size="default">
              获取商品
            </el-button>
            <el-button type="success" @click="handleAddSelectedGoods" size="default">
              添加选中商品
            </el-button>
          </div>
        </div>

        <!-- 第二行：状态按钮和提示信息 -->
        <div class="status-row">
          <div class="status-buttons">
            <el-button :type="goodsDialog.currentStatus === 'onsale' ? 'primary' : 'default'" size="small"
              @click="handleGoodsStatusChange('onsale')">
              在售商品
            </el-button>
            <el-button :type="goodsDialog.currentStatus === 'warehouse' ? 'primary' : 'default'" size="small"
              @click="handleGoodsStatusChange('warehouse')">
              仓库中商品
            </el-button>
            <el-button :type="goodsDialog.currentStatus === 'all' ? 'primary' : 'default'" size="small"
              @click="handleGoodsStatusChange('all')">
              全部商品
            </el-button>
          </div>
          <!-- <div class="info-text">
            商品价格 ¥ 300.00以上大于行为准
          </div> -->
        </div>
      </div>

      <!-- 商品列表表格 -->
      <el-table :data="goodsDialog.goodsList" stripe height="500" :loading="goodsDialog.loading"
        @selection-change="handleGoodsSelection">
        <el-table-column type="selection" width="50"></el-table-column>
        <el-table-column label="序号" type="index" width="60" :index="(index: number) => index + 1"></el-table-column>
        <el-table-column label="宝贝ID" prop="id" width="120">
          <template #default="scope">
            <el-link :underline="false" @click="copyStr(scope.row.id)">{{ scope.row.id }}</el-link>
          </template>
        </el-table-column>
        <el-table-column label="商品名称" prop="goods_name" min-width="300" show-overflow-tooltip></el-table-column>
        <el-table-column label="SKU" width="200">
          <template #default="scope">
            <el-select v-if="scope.row.sku_list && scope.row.sku_list.length > 0" v-model="scope.row._skuSelect"
              placeholder="请选择SKU" size="small" style="width: 100%;" @change="handleSkuChange(scope.row)">
              <el-option v-for="sku in scope.row.sku_list" :key="sku.skuId" :label="sku.spec || scope.row.goods_name"
                :value="sku.skuId">
                <div class="sku-option">
                  <div class="sku-spec">{{ sku.spec || scope.row.goods_name }}</div>
                  <div class="sku-price">¥{{ (sku.groupPrice / 100).toFixed(2) }}</div>
                </div>
              </el-option>
            </el-select>
            <span v-else>-</span>
          </template>
        </el-table-column>
        <el-table-column label="销售价格" width="100" align="right">
          <template #default="scope">
            <span class="price" v-if="scope.row._sku_select_list && scope.row._sku_select_list.length > 0">
              {{ (scope.row._sku_select_list[0].groupPrice / 100).toFixed(2) }}
            </span>
            <span v-else>-</span>
          </template>
        </el-table-column>
        <el-table-column label="库存" width="80" align="right">
          <template #default="scope">
            <span class="stock" v-if="scope.row._sku_select_list && scope.row._sku_select_list.length > 0">
              {{ scope.row._sku_select_list[0].skuQuantity }}
            </span>
            <span v-else>-</span>
          </template>
        </el-table-column>
        <el-table-column label="商品创建时间" width="160" align="center">
          <template #default="scope">
            <span v-if="scope.row.created_at">
              {{ dateFormat(scope.row.created_at) }}
            </span>
            <span v-else>-</span>
          </template>
        </el-table-column>
      </el-table>

      <!-- 弹窗底部操作区 -->
      <div class="dialog-footer">
        <div class="operation-log">
          <div class="log-title">任务日志</div>
          <div class="log-content">
            <div v-for="log in goodsDialog.operationLogs" :key="log.id" class="log-item">
              {{ log.time }} - {{ log.action }} - {{ log.status }}
            </div>
          </div>
        </div>
        <div class="footer-buttons">
          <el-button @click="handleOpenLog">打开日志</el-button>
          <el-button type="danger" @click="goodsDialog.visible = false">关闭</el-button>
        </div>
      </div>
    </el-dialog>

    <!-- 批量输入商品ID弹窗 -->
    <el-dialog v-model="goodsDialog.showBatchInput" title="批量输入商品ID" width="500px">
      <el-input type="textarea" v-model="goodsDialog.batchInputText" placeholder="用逗号,换行隔开" :rows="10" resize="none">
      </el-input>
      <template #footer>
        <el-button @click="goodsDialog.showBatchInput = false">取消</el-button>
        <el-button type="primary" @click="handleBatchInputConfirm">确定</el-button>
      </template>
    </el-dialog>

  </div>
</template>

<script lang="ts" setup>
import dayjs from 'dayjs';
import { ElMessage, TableInstance } from 'element-plus';
import { QuestionFilled, Refresh } from '@element-plus/icons-vue';
import { onActivated, reactive, ref } from 'vue';
import {
  RefundRecord
} from '/@/apis/refund';
import { useUserStore } from "/@/stores/user";
import { storeGoodsList } from '/@/apis/pddStore';
import { useMallStore } from '/@/stores/store';
import { pageStepRequest, dateFormat } from '/@/utils/common';
import request, { updateOrder } from '/@/apis/page';

const userStore = useUserStore()


const getDateUser = async () => {
  userStore.refreshUserInfo()
}

// 商品相关类型定义
interface GoodsItem {
  id: number;
  goods_name: string;
  created_at?: number; // 商品创建时间
  sku_list: Array<{
    skuId: number;
    spec: string;
    groupPrice: number;
    skuQuantity: number;
    isOnsale: boolean;
  }>;
  sold_quantity: number;
  _skuSelect?: number;
  _sku_select_list?: Array<{
    skuId: number;
    spec: string;
    groupPrice: number;
    skuQuantity: number;
  }>;
}

interface OperationLog {
  id: number;
  time: string;
  action: string;
  status: string;
}

// 响应式数据
const refundTableEl = ref<TableInstance>();
const state = reactive({
  refundList: [] as RefundRecord[],
  selection: [] as RefundRecord[]
});

// 申请订单loading状态
const applyOrderLoading = ref(false);

const pagination = reactive({
  page: 1,
  limit: 50,
  total: 0
});

const searchForm = reactive({
  order_status: 101, // 默认选中"全部"状态
  goods_id: '' // 宝贝ID搜索
});

// 商品弹窗相关数据
const goodsDialog = reactive({
  visible: false,
  goodsList: [] as GoodsItem[],
  selectedGoods: [] as GoodsItem[],
  operationLogs: [] as OperationLog[],
  loading: false,
  currentStatus: 'onsale', // 'onsale' | 'warehouse' | 'all'
  searchParams: {
    goods_id: '',
    mallId: '' as '' | number
  },
  showBatchInput: false,
  batchInputText: ''
});

// 店铺相关数据
const mallStore = useMallStore();
const searchState = reactive({
  mallId: '' as '' | number,
  goods_id: '',
});





// 工具函数
const copyStr = (str: string) => {
  navigator.clipboard.writeText(str);
  ElMessage.success('已复制到剪贴板');
};

const getStatusType = (status: number) => {
  const typeMap = {
    101: 'all',      // 全部
    0: 'info',      // 待申请
    1: 'warning',   // 申请中
    2: 'primary',   // 执行中
    3: 'success',   // 已完成
    4: 'danger'     // 执行失败
  };
  return typeMap[status as keyof typeof typeMap] || 'all';
};

const getStatusText = (status: number) => {
  const textMap = {
    101: '全部',
    0: '待申请',
    1: '申请中',
    2: '执行中',
    3: '已完成',
    4: '执行失败'
  };
  return textMap[status as keyof typeof textMap] || '未知';
};

// 业务方法
const getRefundList = async () => {
  try {
    // 构建查询参数
    const params: any = {
      order_status: searchForm.order_status,
      per_page: pagination.limit,
      current_page: pagination.page
    };

    // 如果有宝贝ID搜索条件，添加到参数中
    if (searchForm.goods_id && searchForm.goods_id.trim()) {
      params.goods_id = searchForm.goods_id.trim();
    }

    // 调用真实API接口
    const response = await request({
      url: '/api/ptorder/getList',
      method: 'post',
      data: params,
      encrypt: true
    });

    if (response.code === 0) {
      // 处理返回的数据，转换为前端需要的格式
      // 注意：接口返回的是 response.data.data，不是 response.data.list
      const list = response.data.data || [];
      console.log('接口返回的原始数据:', response.data);
      console.log('待处理的列表数据:', list);

      const transformedList = list.map((item: any) => ({
        id: item.ID, // 接口返回的是大写的 ID
        orderSn: item.order_sn || '',
        shopName: item.shop_name || '',
        goodsId: item.goods_id || '',
        goodsName: item.goods_name || '',
        goodsImg: item.goods_img || '',
        goodsValue: parseFloat(item.goods_price || '0'),
        refundAmount: parseFloat(item.goods_price || '0'),
        createTime: item.add_time ? parseInt(item.add_time) * 1000 : Date.now(), // 时间戳转换
        applyTime: item.add_time ? parseInt(item.add_time) * 1000 : Date.now(),
        processTime: item.server_time ? parseInt(item.server_time) * 1000 : undefined,
        status: item.order_status || 0,
        reason: item.remarks || '',
        remark: item.remarks || '',
        blacklistScore: parseFloat(item.jifen || '0')
      }));

      console.log('转换后的数据:', transformedList);
      state.refundList = transformedList;
      pagination.total = response.data.total || 0;
    } else {
      throw new Error(response.msg || '获取订单列表失败');
    }

  } catch (error) {
    ElMessage.error('获取订单列表失败');
    console.error('获取订单列表失败:', error);
    state.refundList = [];
    pagination.total = 0;
  }
};



const handleStatusFilter = (status: number) => {
  searchForm.order_status = status;
  pagination.page = 1;
  getRefundList();
};

// 宝贝ID搜索
const handleGoodsIdSearch = () => {
  pagination.page = 1;
  getRefundList();
};

// 清空搜索
const handleClearSearch = () => {
  searchForm.goods_id = '';
  pagination.page = 1;
  getRefundList();
};

const handleSyncOrderStatus = async () => {
  try {
    // ElMessage.info('正在同步订单状态...');

    // // 根据当前选中的订单状态，获取对应状态的订单列表进行同步
    // const currentStatus = searchForm.order_status;

    // // 首先获取当前状态下的所有订单
    // const params = {
    //   order_status: currentStatus,
    //   per_page: 1000, // 获取更多数据用于同步
    //   current_page: 1
    // };

    // const response = await request({
    //   url: '/api/ptorder/getList',
    //   method: 'post',
    //   data: params,
    //   encrypt: true
    // });

    // if (response.code === 0 && response.data.data && response.data.data.length > 0) {
    //   // 提取订单ID列表
    //   const orderIds = response.data.data
    //     .map((item: any) => item.goods_id)
    //     .filter((id: string) => id) // 过滤掉空值
    //     .join(',');

    // if (orderIds) {
    //   // 调用同步订单接口
    //   const syncResponse = await updateOrder(
    //     { ids: orderIds },
    //     { showErrorMsg: false }
    //   );

    //   if (syncResponse.code === 0) {
    //     // 同步成功后，重新获取列表数据
    await getRefundList();
    ElMessage.success(`订单状态同步完成`);
    //  ElMessage.success(`订单状态同步完成，共同步 ${response.data.data.length} 个订单`);
    //   } else {
    //     throw new Error(syncResponse.msg || '同步订单状态失败');
    //   }
    // } else {
    //   ElMessage.warning('当前状态下没有可同步的订单');
    // }
    // } else {
    //   ElMessage.warning('当前状态下没有订单数据');
    // }

  } catch (error) {
    console.error('同步订单状态失败:', error);
    ElMessage.error(error instanceof Error ? error.message : '同步失败，请重试');
  }
};

// 商品弹窗相关方法
const handleGetGoods = () => {
  goodsDialog.visible = true;
  // 如果弹窗中没有选择店铺，使用主界面的店铺选择
  if (!goodsDialog.searchParams.mallId && searchState.mallId) {
    goodsDialog.searchParams.mallId = searchState.mallId;
  }
  // 只打开弹窗，不自动加载数据
  loadMockOperationLogs();
};

const handleGoodsSelection = (selection: GoodsItem[]) => {
  goodsDialog.selectedGoods = selection;
};

const handleOpenLog = () => {
  ElMessage.info('打开日志功能');
};

// 处理批量输入确认
const handleBatchInputConfirm = () => {
  // 解析输入的文本，支持逗号和换行分隔
  const resolveInputStr = (str: string): string[] => {
    return str.split(/[,，\n]/).map(item => item.trim()).filter(item => item);
  };

  goodsDialog.searchParams.goods_id = resolveInputStr(goodsDialog.batchInputText).join(',');
  goodsDialog.showBatchInput = false;
  loadGoodsData();
};

// 处理店铺变更
const handleStoreChange = () => {
  loadGoodsData();
};

// 处理SKU变更
const handleSkuChange = (goodsItem: GoodsItem) => {
  if (goodsItem._skuSelect && goodsItem.sku_list) {
    const selectedSku = goodsItem.sku_list.find(sku => sku.skuId === goodsItem._skuSelect);
    if (selectedSku) {
      goodsItem._sku_select_list = [selectedSku];
    }
  }
};

// 处理商品状态切换
const handleGoodsStatusChange = (status: string) => {
  goodsDialog.currentStatus = status;
  loadGoodsData();
};

// 动态加载商品数据
const loadGoodsData = async () => {
  // 检查是否选择了店铺（优先使用弹窗中的店铺选择，其次使用主界面的）
  const selectedMallId = goodsDialog.searchParams.mallId || searchState.mallId;
  const mall = mallStore.tableList.find(item => item.mallId == selectedMallId);
  if (!mall) {
    ElMessage.warning('请先选择店铺');
    return;
  }

  if (!mallStore.checkAvailable(mall)) {
    ElMessage.warning('店铺不可用');
    return;
  }

  goodsDialog.loading = true;

  try {
    // 根据当前状态设置查询参数
    const goodsIdList = goodsDialog.searchParams.goods_id ?
      goodsDialog.searchParams.goods_id.split(',').filter(item => item.trim()) :
      undefined;

    let queryParams: any = {
      page: 1,
      goods_id_list: goodsIdList
    };

    // 根据状态设置不同的查询条件
    switch (goodsDialog.currentStatus) {
      case 'onsale':
        queryParams.is_onsale = 1;
        queryParams.sold_out = 0;
        break;
      case 'warehouse':
        queryParams.is_onsale = 0;
        break;
      case 'all':
        // 不设置特定条件，获取所有商品
        break;
    }

    // 使用分页请求获取所有商品数据
    const allList = await pageStepRequest({
      delay: 1000,
      pageStart: 1,
      pageEnd: 10, // 最多获取10页数据
      isStop: () => !goodsDialog.loading,
      request: async (page) => {
        const { data: res } = await storeGoodsList({
          ...queryParams,
          page
        }, mall!);

        if (!res.success) {
          return Promise.reject(new Error(res.error_msg || '获取商品列表失败'));
        }

        const goodsList: GoodsItem[] = res.result.goods_list || [];

        // 处理商品数据，设置默认SKU选择
        goodsList.forEach(item => {
          if (item.sku_list && item.sku_list.length > 0) {
            // 根据当前状态决定是否过滤SKU
            if (goodsDialog.currentStatus === 'onsale') {
              // 在售商品状态下，只显示在售的SKU
              item.sku_list = item.sku_list.filter(sku => sku.isOnsale);
            }
            // 其他状态下显示所有SKU

            if (item.sku_list.length > 0) {
              // 按价格排序，选择最低价的SKU作为默认选择
              const sortedSkus = [...item.sku_list].sort((a, b) => a.groupPrice - b.groupPrice);
              const selectedSku = sortedSkus[0];
              item._skuSelect = selectedSku.skuId;
              item._sku_select_list = [selectedSku];
            }
          }
        });

        const total = res.result.total || 0;
        return { list: goodsList, total };
      }
    });

    goodsDialog.goodsList = allList;

    // 添加操作日志
    addOperationLog({
      action: '获取商品',
      status: `共获取${allList.length}个商品`
    });

    addOperationLog({
      action: '获取成功',
      status: `共获取${allList.length}个商品`
    });

  } catch (error) {
    console.error('获取商品列表失败:', error);
    ElMessage.error('获取商品列表失败');
    goodsDialog.goodsList = [];

    addOperationLog({
      action: '获取商品',
      status: '获取失败'
    });
  } finally {
    goodsDialog.loading = false;
  }
};


// 添加操作日志的辅助方法
const addOperationLog = (logData: { action: string; status: string }) => {
  const now = new Date();
  const timeStr = `${now.getFullYear()}-${String(now.getMonth() + 1).padStart(2, '0')}-${String(now.getDate()).padStart(2, '0')} ${String(now.getHours()).padStart(2, '0')}:${String(now.getMinutes()).padStart(2, '0')}:${String(now.getSeconds()).padStart(2, '0')}`;

  goodsDialog.operationLogs.push({
    id: Date.now(),
    time: timeStr,
    action: logData.action,
    status: logData.status
  });
};



// 加载模拟操作日志
const loadMockOperationLogs = () => {
  // goodsDialog.operationLogs = [
  //   {
  //     id: 1,
  //     time: '2025-07-21 20:20:13',
  //     action: '获取商品',
  //     status: '共获取13个商品'
  //   },
  //   {
  //     id: 2,
  //     time: '2025-07-21 20:20:13',
  //     action: '获取成功',
  //     status: '共获取147个商品'
  //   },
  //   {
  //     id: 3,
  //     time: '2025-07-21 20:22:56',
  //     action: '获取商品',
  //     status: '共获取147个商品'
  //   },
  //   {
  //     id: 4,
  //     time: '2025-07-21 20:22:57',
  //     action: '获取成功',
  //     status: '共获取147个商品'
  //   }
  // ];
};



// 添加选中商品到退款管理
const handleAddSelectedGoods = async () => {
  if (goodsDialog.selectedGoods.length === 0) {
    ElMessage.warning('请先选择要添加的商品');
    return;
  }

  // 时间验证逻辑：检查商品是否超过30天
  const currentTime = Date.now();
  const maxDays = userStore.pait.day;
  console.log(userStore.pait.combo, '2222222222222222222222222')
  const msPerDay = 24 * 60 * 60 * 1000; // 一天的毫秒数

  for (const item of goodsDialog.selectedGoods) {
    if (item.created_at) {
      // 将商品创建时间转换为毫秒（如果是秒级时间戳需要乘以1000）
      const goodsCreateTime = typeof item.created_at === 'number'
        ? (item.created_at < 10000000000 ? item.created_at * 1000 : item.created_at)
        : new Date(item.created_at).getTime();

      // 计算天数差
      const daysDiff = Math.floor((currentTime - goodsCreateTime) / msPerDay);

      if (daysDiff > maxDays) {
        ElMessage.error(`${item.goods_name} 商品已超${userStore.pait.day}过天`);

        // 添加操作日志
        addOperationLog({
          action: '时间验证',
          status: `${item.goods_name} 超过${userStore.pait.day}天，添加失败`
        });

        return; // 阻止继续执行
      }
    }
  }

  try {
    // 获取当前选中的店铺信息
    const selectedMallId = goodsDialog.searchParams.mallId || searchState.mallId;
    const selectedMall = mallStore.tableList.find(item => item.mallId == selectedMallId);
    const shopName = selectedMall?.mallName || '';

    // 构建请求参数
    const goods = goodsDialog.selectedGoods.map(item => {
      // 获取选中的SKU信息
      const selectedSku = item._sku_select_list && item._sku_select_list.length > 0
        ? item._sku_select_list[0]
        : null;

      if (!selectedSku) {
        throw new Error(`商品 ${item.goods_name} 未选择SKU`);
      }

      // 获取销售价格（转换为元）
      const salesPrice = selectedSku.groupPrice / 100;

      // 价格验证逻辑：根据 userStore.pait.combo 数组进行价格验证和积分赋值
      let jifen = salesPrice.toFixed(2); // 默认积分等于价格
      let priceValid = false;

      // 遍历 combo 数组，查找符合条件的价格配置
      if (userStore.pait.combo && Array.isArray(userStore.pait.combo)) {
        for (const comboItem of userStore.pait.combo) {
          const prefixPrice = parseFloat(comboItem.prefix);

          // 如果销售价格小于当前 prefix 值，则使用该配置的 price 作为 jifen
          if (salesPrice < prefixPrice) {
            jifen = parseFloat(comboItem.price).toFixed(2);
            priceValid = true;
            break;
          }
        }
      }

      // 如果价格不符合任何配置，抛出错误
      if (!priceValid) {
        throw new Error('该价格无效');
      }

      // 获取当前商品的创建时间
      const goodsCreateTime = item.created_at || '';

      return {
        goods_name: item.goods_name,
        goods_id: item.id.toString(),
        goods_price: salesPrice.toFixed(2), // 转换为元
        jifen: jifen, // 根据价格验证逻辑计算的积分
        shop_name: shopName, // 店铺名称
        goods_add_time: goodsCreateTime // 商品创建时间，取当前商品的created_at字段
      };
    });

    const requestData = { goods };

    // 调用API，使用项目统一的加密方式
    const response = await request({
      url: '/api/ptorder/add',
      method: 'post',
      data: requestData,
      encrypt: true // 使用项目统一的加密处理
    });
    if (response.code === 0) {
      ElMessage.success(`成功添加 ${goods.length} 个商品`);

      // 添加操作日志
      addOperationLog({
        action: '添加商品',
        status: `成功添加${goods.length}个商品`
      });

      // 清空选中状态
      goodsDialog.selectedGoods = [];

      // 关闭弹窗
      goodsDialog.visible = false;

      // 刷新退款列表
      await getRefundList();
    } else {
      throw new Error(response.msg);
    }

  } catch (error) {
    console.error('添加选中商品失败:', error);
    ElMessage.error(error instanceof Error ? error.message : '添加商品失败，请重试');

    // 添加错误日志
    addOperationLog({
      action: '添加商品',
      status: '添加失败'
    });
  }
};

// 申请订单
const handleApplyOrder = async () => {
  if (state.selection.length === 0) {
    ElMessage.warning('请先勾选要申请的订单');
    return;
  }

  try {
    // 构建请求参数
    const goods_id = state.selection.map(item => item.goodsId);

    const requestData = {
      type: 'start',
      goods_id: goods_id
    };

    // 调用API
    const response = await request({
      url: '/api/ptorder/setOrder',
      method: 'post',
      data: requestData,
      encrypt: true
    });

    if (response.code === 0) {
      ElMessage.success(`成功申请 ${goods_id.length} 个订单`);

      // 刷新列表
      await getRefundList();
      // 刷新金额
      await userStore.refreshUserInfo()

      // 清空选中状态
      state.selection = [];
      refundTableEl.value?.clearSelection();
    } else {
      throw new Error(response.msg || '申请订单失败');
    }

  } catch (error) {
    console.error('申请订单失败:', error);
    ElMessage.error(error instanceof Error ? error.message : '申请订单失败，请重试');
  }
};

// 删除订单
const handleDeleteOrder = async () => {
  if (state.selection.length === 0) {
    ElMessage.warning('请先勾选要删除的订单');
    return;
  }

  try {
    // 构建请求参数
    const goods_id = state.selection.map(item => item.goodsId);

    const requestData = {
      type: 'is_del',
      goods_id: goods_id
    };

    // 调用API
    const response = await request({
      url: '/api/ptorder/setOrder',
      method: 'post',
      data: requestData,
      encrypt: true
    });

    if (response.code === 0) {
      ElMessage.success(`成功删除 ${goods_id.length} 个订单`);

      // 刷新列表
      await getRefundList();

      // 清空选中状态
      state.selection = [];
      refundTableEl.value?.clearSelection();
    } else {
      throw new Error(response.msg || '删除订单失败');
    }

  } catch (error) {
    console.error('删除订单失败:', error);
    ElMessage.error(error instanceof Error ? error.message : '删除订单失败，请重试');
  }
};

// 生命周期
onActivated(() => {
  getRefundList();
});
</script>

<style lang="scss" scoped>
.el-tag--danger {
  --el-color-danger-light-9: #fcc8e0;
  --el-color-danger-light-8: #fc8fc2;
  --el-color-danger-light-7: #f76cac;
  --el-color-danger-light-5: #f357a0;
  --el-color-danger-light-3: #dd287c;
  --el-color-danger: #dd287c;
  --el-color-danger-dark-2: #dd287c;
}

h5 {
  top: 20px;
  content: "";
  position: relative;
  margin: 3px 0 0 12px;
  width: 3px;
  height: 18px;
  background: var(--el-color-primary);
}

.box {
  width: 100%;

  .tips {
    color: var(--el-text-color-secondary);
    font-size: 12px;
  }

  h4.title {
    width: 100%;
    height: 30px;
    box-sizing: border-box;
    padding-bottom: 8px;
    padding-left: 24px;
    font-size: 14px;
    line-height: 22px;
  }

  div.container {
    width: 100%;
    height: 280px;
    background-color: #f8faff;
    border: 1px solid var(--el-border-color-darker);
    border-radius: 6px;
    padding: 22px;
    box-sizing: border-box;
  }

  .el-checkbox {
    :deep(.el-checkbox__label) {
      color: var(--el-text-color-primary);
    }
  }
}

.refund-manage {
  overflow: hidden;

  .user-info-bar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
    padding: 12px 16px;
    background: #f5f7fa;
    border-radius: 4px;
    border: 1px solid #e4e7ed;

    .user-info {
      display: flex;
      align-items: center;
      gap: 8px;
      font-size: 14px;

      .user-label,
      .points-label,
      .store-label {
        color: #606266;
      }

      .user-id {
        color: #303133;
        font-weight: 500;
      }

      .points-value {
        color: #409eff;
        font-weight: 500;
      }

      .help-btn,
      .refresh-btn {
        margin-left: 4px;
        width: 24px;
        height: 24px;
      }

      .store-select {
        display: flex;
        align-items: center;
        gap: 8px;
        margin-left: 16px;
      }
    }

    .action-buttons {
      display: flex;
      gap: 8px;
    }
  }

  .status-tabs {
    width: 100%;
    margin-bottom: 16px;
    padding: 16px;
    background: #f5f7fa;
    border-radius: 4px;

    .el-space {
      width: 100%;
      align-items: center;
    }

    .search-area {
      width: 100%;
      display: flex;
      align-items: center;
      justify-content: space-between;
      gap: 8px;
    }

    .search-body {
      margin-left: 10rem;
    }
  }

  .illustrate-tabs {
    margin-top: 16px;
    padding-bottom: 6px;
    background: #ffffff;
    border-radius: 4px;

    .el-space {
      width: 100%;
      align-items: center;
    }
  }

  .refund-table {
    @include commonTableHeader();
    border: 1px solid var(--el-border-color-darker);

    .goods-info {
      display: flex;
      align-items: center;
    }

    .amount {
      color: #f56c6c;
      font-weight: bold;
    }

    .score {
      color: #409eff;
      font-weight: bold;
    }

    .el-space {
      width: 100%;
      justify-content: flex-end;
    }
  }
}

// 商品弹窗样式
:deep(.goods-dialog) {
  position: relative;
  top: -3.5em;

  .el-dialog__body {
    padding: 0px 20px 20px 20px;

  }

  .dialog-header {
    margin-bottom: 16px;
    padding: 16px 0;
    border-bottom: 1px solid #e4e7ed;

    .search-row {
      display: flex;
      align-items: center;
      margin-bottom: 12px;

      .search-controls {
        display: flex;
        align-items: center;
        gap: 8px;
      }

      .right-controls {
        display: flex;
        align-items: center;
        gap: 16px;
      }

      .store-select-wrapper {
        display: flex;
        align-items: center;
        gap: 8px;

        .store-label {
          color: #606266;
          font-size: 14px;
        }
      }
    }

    .status-row {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .status-buttons {
        display: flex;
        gap: 8px;
      }

      .info-text {
        color: #606266;
        font-size: 14px;
      }
    }
  }

  .el-overlay-message-box,
  .el-overlay,
  .el-overlay-dialog {
    overflow: hidden;
  }

  .el-table {
    .price {
      color: #f56c6c;
      font-weight: bold;
    }

    .stock {
      color: #409eff;
      font-weight: bold;
    }

    .sku-option {
      display: flex;
      justify-content: space-between;
      align-items: center;
      width: 100%;

      .sku-spec {
        flex: 1;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }

      .sku-price {
        color: #f56c6c;
        font-weight: bold;
        margin-left: 8px;
        flex-shrink: 0;
      }
    }
  }

  .dialog-footer {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-top: 16px;
    padding-top: 16px;
    border-top: 1px solid #e4e7ed;

    .operation-log {
      flex: 1;
      margin-right: 20px;

      .log-title {
        font-weight: bold;
        margin-bottom: 8px;
        color: #303133;
      }

      .log-content {
        max-height: 100px;
        overflow-y: auto;
        background: #f5f7fa;
        padding: 8px;
        border-radius: 4px;
        border: 1px solid #e4e7ed;

        .log-item {
          font-size: 12px;
          color: #606266;
          line-height: 1.5;
          margin-bottom: 4px;

          &:last-child {
            margin-bottom: 0;
          }
        }
      }
    }

    .footer-buttons {
      display: flex;
      gap: 8px;
      align-items: flex-end;
    }
  }
}
</style>
